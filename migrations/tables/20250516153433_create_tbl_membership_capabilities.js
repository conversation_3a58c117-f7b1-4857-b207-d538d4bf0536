const SCHEMA = "membership";
const TABLE_NAME = "tbl_membership_capabilities";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.uuid("membership_id").notNullable();
    table.uuid("capability_id").notNullable();
    table.string("value").notNullable();
    table.timestamp("created_at", { useTz: true }).notNullable();
    table.timestamp("updated_at", { useTz: true }).nullable();

    // Foreign keys
    table
      .foreign("membership_id")
      .references("id")
      .inTable(`${SCHEMA}.tbl_memberships`);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
