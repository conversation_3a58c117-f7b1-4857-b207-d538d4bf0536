const SCHEMA = "membership";
const TABLE_NAME = "tbl_membership_external_mappings";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.uuid("membership_id").notNullable();
    table.string("provider", 100).notNullable();
    table.string("external_membership_id").notNullable();
    table.timestamp("created_at", { useTz: true }).notNullable();
    table.timestamp("updated_at", { useTz: true }).nullable();

    // Foreign key
    table
      .foreign("membership_id")
      .references("id")
      .inTable(`${SCHEMA}.tbl_memberships`);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
