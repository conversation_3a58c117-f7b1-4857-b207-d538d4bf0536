const SCHEMA = "membership";
const TABLE_NAME = "tbl_tenant_membership_external_mappings";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.uuid("tenant_membership_id").notNullable();
    table.string("external_tenant_membership_id").notNullable();
    table.string("external_customer_id").notNullable();
    table.string("provider", 100).notNullable();
    table.timestamp("created_at", { useTz: true }).notNullable();
    table.timestamp("updated_at", { useTz: true }).nullable();

    // Foreign key
    table
      .foreign("tenant_membership_id")
      .references("id")
      .inTable(`${SCHEMA}.tbl_tenant_membership`);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
