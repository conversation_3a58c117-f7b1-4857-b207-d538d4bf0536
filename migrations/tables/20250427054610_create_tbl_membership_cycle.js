const SCHEMA = "membership";
const TABLE_NAME = "tbl_membership_cycles";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.uuid("membership_id").notNullable();
    table.string("billing_cycle", 20).notNullable(); // monthly or yearly
    table.decimal("amount", 8, 2).notNullable();
    table.timestamp("created_at", { useTz: true }).notNullable();
    table.string("created_by").notNullable();
    table.timestamp("updated_at", { useTz: true }).nullable();
    table.string("updated_by").nullable();

    // Foreign key
    table
      .foreign("membership_id")
      .references("id")
      .inTable(`${SCHEMA}.tbl_memberships`);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
