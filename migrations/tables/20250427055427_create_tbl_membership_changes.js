const SCHEMA = "membership";
const TABLE_NAME = "tbl_membership_changes";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.uuid("tenant_membership_id").notNullable();
    table.bigInteger("start_at").notNullable();
    table.bigInteger("end_at").nullable();
    table.bigInteger("expire_at").notNullable();

    // Foreign key
    table
      .foreign("tenant_membership_id")
      .references("id")
      .inTable(`${SCHEMA}.tbl_tenant_membership`);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
