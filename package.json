{"name": "membership-module", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/src/main.js", "build": "node build.js && tsc --build tsconfig.json && tsc-alias", "nodemon": "nodemon --config nodemon.debug.json", "test": "echo \"Error: no test specified\" && exit 1", "migrate:latest": "npx knex migrate:latest --env production"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@cbidigital/aqua-ddd": "^1.0.0-rc.9", "@heronjs/common": "3.3.40", "@heronjs/core": "3.4.40", "@heronjs/express": "^3.1.7", "axios": "^1.9.0", "class-validator": "^0.14.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "pg": "^8.15.6", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "stripe": "^18.1.0", "tsc-alias": "^1.8.15", "zod": "^3.24.3"}, "devDependencies": {"@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.14.1", "fs-extra": "^11.3.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}