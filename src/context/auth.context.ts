import {
  APIError,
  <PERSON>th<PERSON><PERSON><PERSON><PERSON>,
  SecureContext,
  SecureProperty,
} from "@heronjs/common";
import JWT from "jsonwebtoken";
import { Observable, of } from "rxjs";
import { StatusCodes } from "http-status-codes";

export class JwtUtil {
  public static createTokens(
    payload: object,
    privateKey: string,
    expiresIn?: number
  ) {
    const signOptions: JWT.SignOptions = {
      algorithm: "RS256",
      expiresIn: expiresIn,
    };

    const token = JWT.sign(payload, privateKey, signOptions);

    return token;
  }

  public static verifyToken(token: string, publicKey: string) {
    const result = JWT.verify(token, publicKey);

    return result;
  }

  public static decode(token: string) {
    const result = JWT.decode(token);

    return result;
  }
}

export type PublicKeysDTO = {
  accessTokenPublicKey: string;
  refreshTokenPublicKey: string;
};

export class UnauthorizedError extends APIError {
  constructor() {
    super(StatusCodes.UNAUTHORIZED, "Unauthorized");
  }
}

export type SampleSecurityType = {
  private: boolean;
  organization: string;
  sub: string;
};

export class AuthContext
  implements SecureContext<SampleSecurityType, SecureProperty>
{
  OnGuard(data: SampleSecurityType): Observable<SecureProperty> {
    return of(data as any);
  }

  static Resolver: AuthResolver<SampleSecurityType> = {
    http: ["header", "authorization"],
    ws: ["handshake", "token"],

    resolve: async (data?: string): Promise<SampleSecurityType> => {
      return {
        private: true,
        sub: "3d948263-bc07-4f12-949d-05073a19ed8d",
        // organization: 'dev_cd2aeda11835470f9d71b58efc293a0f',
        organization: "dev_a4aa7617163f4a7a80d4058d37b84bf6", // Ann clone
      };
    },
  };
}
