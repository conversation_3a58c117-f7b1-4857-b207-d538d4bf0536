export const MEMBERSHIP_INJECT_TOKENS = {
  REPOSITORY: {
    MEMBERSHIP: Symbol("MEMBERSHIP_REPOSITORY").toString(),
    TENANT_MEMBERSHIP: Symbol("TENANT_MEMBERSHIP_REPOSITORY").toString(),
    CUSTOMER_EXTERNAL_MAPPING: Symbol(
      "CUSTOMER_EXTERNAL_MAPPING_REPOSITORY"
    ).toString(),
    CAPABILITY: Symbol("CAPABILITY_REPOSITORY").toString(),
    MEMBERSHIP_CAPABILITY: Symbol(
      "MEMBERSHIP_CAPABILITY_REPOSITORY"
    ).toString(),
  },
  BUILDER: {
    MEMBERSHIP: Symbol("MEMBERSHIP_BUILDER").toString(),
    TENANT_MEMBERSHIP: Symbol("TENANT_MEMBERSHIP_BUILDER").toString(),
    CAPABILITY: Symbol("CAPABILITY_BUILDER").toString(),
  },
  USECASE: {
    // Membership
    CREATE_MEMBERSHIP: Symbol("CREATE_MEMBERSHIP_USECASE").toString(),
    UPDATE_MEMBERSHIP: Symbol("UPDATE_MEMBERSHIP_USECASE").toString(),
    DELETE_MEMBERSHIP: Symbol("DELETE_MEMBERSHIP_USECASE").toString(),
    GET_MEMBERSHIP_BY_ID: Symbol("GET_MEMBERSHIP_BY_ID_USECASE").toString(),
    GET_LIST_OF_MEMBERSHIPS: Symbol(
      "GET_LIST_OF_MEMBERSHIPS_USECASE"
    ).toString(),
    ADD_MEMBERSHIP_CYCLE: Symbol("ADD_MEMBERSHIP_CYCLE_USECASE").toString(),

    // Capability
    CREATE_CAPABILITY: Symbol("CREATE_CAPABILITY_USECASE").toString(),
    GET_LIST_OF_CAPABILITIES: Symbol(
      "GET_LIST_OF_CAPABILITIES_USECASE"
    ).toString(),
    GET_TENANT_MEMBERSHIP: Symbol("GET_TENANT_MEMBERSHIP_USECASE").toString(),

    // Membership Capability
    ASSIGN_CAPABILITY_TO_MEMBERSHIP: Symbol(
      "ASSIGN_CAPABILITY_TO_MEMBERSHIP_USECASE"
    ).toString(),
    REMOVE_CAPABILITY_FROM_MEMBERSHIP: Symbol(
      "REMOVE_CAPABILITY_FROM_MEMBERSHIP_USECASE"
    ).toString(),
    GET_MEMBERSHIP_CAPABILITIES: Symbol(
      "GET_MEMBERSHIP_CAPABILITIES_USECASE"
    ).toString(),

    // Tenant Membership
    ASSIGN_TENANT_MEMBERSHIP: Symbol(
      "ASSIGN_TENANT_MEMBERSHIP_USECASE"
    ).toString(),
    EXTEND_TENANT_TRIAL_PERIOD: Symbol(
      "EXTEND_TENANT_TRIAL_PERIOD_USECASE"
    ).toString(),
    UPDATE_TENANT_MEMBERSHIP: Symbol(
      "UPDATE_TENANT_MEMBERSHIP_USECASE"
    ).toString(),
    CANCEL_TENANT_MEMBERSHIP: Symbol(
      "CANCEL_TENANT_MEMBERSHIP_USECASE"
    ).toString(),
    EXPIRE_TENANT_MEMBERSHIP: Symbol(
      "EXPIRE_TENANT_MEMBERSHIP_USECASE"
    ).toString(),
    GET_TENANT_MEMBERSHIP_BY_ID: Symbol(
      "GET_TENANT_MEMBERSHIP_BY_ID_USECASE"
    ).toString(),
    GET_TENANT_MEMBERSHIP_BY_TENANT_ID: Symbol(
      "GET_TENANT_MEMBERSHIP_BY_TENANT_ID_USECASE"
    ).toString(),
    ACTIVATE_EXTERNAL_SUBSCRIPTION: Symbol(
      "ACTIVATE_EXTERNAL_SUBSCRIPTION_USECASE"
    ).toString(),

    // Notifications
    NOTIFY_SUBSCRIPTION_EXPIRY: Symbol(
      "NOTIFY_SUBSCRIPTION_EXPIRY_USECASE"
    ).toString(),

    // Customer
    CREATE_CUSTOMER_EXTERNAL_MAPPING: Symbol(
      "CREATE_CUSTOMER_EXTERNAL_MAPPING_USECASE"
    ).toString(),

    // Payment Gateway
    HANDLE_WEBHOOK_EVENT: Symbol("HANDLE_WEBHOOK_EVENT_USECASE").toString(),
  },
  DAO: {
    MEMBERSHIP: Symbol("MEMBERSHIP_DAO").toString(),
    MEMBERSHIP_CYCLE: Symbol("MEMBERSHIP_CYCLE_DAO").toString(),
    TENANT_MEMBERSHIP: Symbol("TENANT_MEMBERSHIP_DAO").toString(),
    MEMBERSHIP_CHANGE: Symbol("MEMBERSHIP_CHANGE_DAO").toString(),
    MEMBERSHIP_EXTERNAL_MAPPING: Symbol(
      "MEMBERSHIP_EXTERNAL_MAPPING_DAO"
    ).toString(),
    MEMBERSHIP_CYCLE_EXTERNAL_MAPPING: Symbol(
      "MEMBERSHIP_CYCLE_EXTERNAL_MAPPING_DAO"
    ).toString(),
    TENANT_MEMBERSHIP_EXTERNAL_MAPPING: Symbol(
      "TENANT_MEMBERSHIP_EXTERNAL_MAPPING_DAO"
    ).toString(),
    CUSTOMER_EXTERNAL_MAPPING: Symbol(
      "CUSTOMER_EXTERNAL_MAPPING_DAO"
    ).toString(),
    CAPABILITY: Symbol("CAPABILITY_DAO").toString(),
    MEMBERSHIP_CAPABILITY: Symbol("MEMBERSHIP_CAPABILITY_DAO").toString(),
  },
  MAPPER: {
    MEMBERSHIP: Symbol("MEMBERSHIP_MAPPER").toString(),
    MEMBERSHIP_EXTERNAL_MAPPING: Symbol(
      "MEMBERSHIP_EXTERNAL_MAPPING_MAPPER"
    ).toString(),
    MEMBERSHIP_CYCLE: Symbol("MEMBERSHIP_CYCLE_MAPPER").toString(),
    MEMBERSHIP_CYCLE_EXTERNAL_MAPPING: Symbol(
      "MEMBERSHIP_CYCLE_EXTERNAL_MAPPING_MAPPER"
    ).toString(),
    TENANT_MEMBERSHIP: Symbol("TENANT_MEMBERSHIP_MAPPER").toString(),
    TENANT_MEMBERSHIP_EXTERNAL_MAPPING: Symbol(
      "TENANT_MEMBERSHIP_EXTERNAL_MAPPING_MAPPER"
    ).toString(),
    CUSTOMER_EXTERNAL_MAPPING: Symbol(
      "CUSTOMER_EXTERNAL_MAPPING_MAPPER"
    ).toString(),
    CAPABILITY: Symbol("CAPABILITY_MAPPER").toString(),
    MEMBERSHIP_CAPABILITY: Symbol("MEMBERSHIP_CAPABILITY_MAPPER").toString(),
    MEMBERSHIP_CHANGE: Symbol("MEMBERSHIP_CHANGE_MAPPER").toString(),
  },
  SERVICE: {
    NOTIFICATION: Symbol("NOTIFICATION_SERVICE").toString(),
    USER_PROFILE: Symbol("USER_PROFILE_SERVICE").toString(),
    PAYMENT: Symbol("PAYMENT_SERVICE").toString(),
  },
  FACTORY: {
    PAYMENT_GATEWAY: Symbol("PAYMENT_GATEWAY_FACTORY").toString(),
    WEBHOOK_HANDLER: Symbol("WEBHOOK_HANDLER_FACTORY").toString(),
  },
  UTIL: {
    RETRY: Symbol("RETRY_UTIL").toString(),
    DATABASE: Symbol("DATABASE_UTIL").toString(),
  },
  HANDLER: {
    PAYMENT_SUCCEEDED: Symbol("PAYMENT_SUCCEEDED_HANDLER").toString(),
  },

  PAYMENT_GATEWAY: {
    STRIPE: Symbol("STRIPE_PAYMENT_GATEWAY").toString(),
    PAYPAL: Symbol("PAYPAL_PAYMENT_GATEWAY").toString(),
  },
};
