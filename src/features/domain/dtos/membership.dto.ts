import { Nullable, Optional } from "@heronjs/common";
import { MembershipStatusEnum } from "@features/domain/aggregates";
import { MembershipCycleDto } from "@features/domain/dtos/membership-cycle.dto";
import { MembershipCapabilityDto } from "@features/domain/dtos/membership-capability.dto";

export type MembershipDto = {
  id: string;
  tenantId: Nullable<string>;
  name: string;
  isCustom: boolean;
  isTrial: boolean;
  status: MembershipStatusEnum;
  description: string;
  createdAt: Date;
  createdBy: string;
  createdByName?: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  updatedByName?: string;
  cycles: Nullable<Partial<MembershipCycleDto>[]>;
  capabilities: Nullable<MembershipCapabilityDto[]>;
  upsertedCycles?: MembershipCycleDto[];
  upsertedCapabilities?: MembershipCapabilityDto[];
  deletedCycles?: MembershipCycleDto[];
  deletedCapabilities?: MembershipCapabilityDto[];
};
