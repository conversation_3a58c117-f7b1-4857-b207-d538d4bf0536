export enum PaymentGatewayProvider {
  STRIPE = "stripe",
  PAYPAL = "paypal",
}

export const PAYMENT_EVENT_TYPE = {
  PAYMENT_SUCCEEDED: ["invoice.payment_succeeded", "invoice.paid"],
  PAYMENT_FAILED: ["invoice.payment_failed"],
  SUBSCRIPTION_CREATED: ["checkout.session.completed"],
  SUBSCRIPTION_UPDATED: ["customer.subscription.updated"],
  SUBSCRIPTION_CANCELLED: ["customer.subscription.deleted"],
};

export type EventPayload = {
  body: string | Buffer | any;
  headers: Record<string, string>;
};

export type CreatePaymentGatewayProductInput = {
  name: string;
  description: string;
  metadata: Record<string, any>;
};

export type PaymentGatewayProduct = {
  id: string;
};

type Interval = "day" | "month" | "week" | "year";

export type CreatePaymentGatewayPriceInput = {
  productId: string;
  amount: number; // in cents
  currency: string;
  recurring: {
    interval: Interval;
    intervalCount: number;
  };
  metadata: Record<string, any>;
};

export type PaymentGatewayPrice = {
  id: string;
};

export type CreatePaymentGatewaySubscriptionInput = {
  customerId: string;
  priceId: string;
  metadata: Record<string, any>;
};

export type PaymentGatewaySubscription = {
  id: string;
  customerId: string;
  paymentIntent?: Record<string, any>;
};

export type UpdatePaymentGatewaySubscriptionInput = {
  subscriptionId: string;
  priceId: string;
  metadata: Record<string, any>;
};

export type UpdatePaymentGatewaySubscriptionOutput = {
  id: string;
};

export type CancelPaymentGatewaySubscriptionInput = {
  subscriptionId: string;
};

export type CancelPaymentGatewaySubscriptionOutput = {
  id: string;
};

export type CreatePaymentGatewayCustomerInput = {
  email: string;
  name: string;
  metadata: Record<string, any>;
};

export type CreatePaymentGatewayCustomerOutput = {
  id: string;
};

export type UpdatePaymentGatewayCustomerInput = {
  customerId: string;
  email: string;
  name: string;
  metadata: Record<string, any>;
};

export type UpdatePaymentGatewayCustomerOutput = {
  id: string;
};

export type PaymentSucceededEvent = {
  provider: PaymentGatewayProvider;
  type: string;
  data: {
    subscription: string;
    invoice: string;
    customer: string;
    metadata: {
      tenantMembershipId: string;
    };
  };
};

export type PaymentFailedEvent = {
  provider: PaymentGatewayProvider;
  type: string;
  data: {
    subscription: string;
    invoice: string;
    metadata: {
      tenantMembershipId?: string;
    };
  };
};

export type PaymentEvent =
  | PaymentSucceededEvent
  | PaymentFailedEvent
  | Record<string, any>; // add another types to this if needed

export type Subscription = {
  id: string;
  customer: string;
  invoice: string;
  startAt: number;
  expireAt: number;
  metadata: Record<string, any>;
};

export type Invoice = {
  id: string;
  created: number;
};

export interface IPaymentGateway {
  parseEvent(input: any): Promise<PaymentEvent>;
  getSubscription(subscriptionId: string): Promise<Subscription>;
  getInvoice(invoiceId: string): Promise<Invoice>;
  createProduct(
    input: CreatePaymentGatewayProductInput
  ): Promise<PaymentGatewayProduct>;
  createPrice(
    input: CreatePaymentGatewayPriceInput
  ): Promise<PaymentGatewayPrice>;
  createSubscription(
    input: CreatePaymentGatewaySubscriptionInput
  ): Promise<PaymentGatewaySubscription>;
  updateSubscription(
    input: UpdatePaymentGatewaySubscriptionInput
  ): Promise<UpdatePaymentGatewaySubscriptionOutput>;
  cancelSubscription(
    input: CancelPaymentGatewaySubscriptionInput
  ): Promise<CancelPaymentGatewaySubscriptionOutput>;
  createCustomer(
    input: CreatePaymentGatewayCustomerInput
  ): Promise<CreatePaymentGatewayCustomerOutput>;
  updateCustomer(
    input: UpdatePaymentGatewayCustomerInput
  ): Promise<UpdatePaymentGatewayCustomerOutput>;
}

export interface IPaymentGatewayFactory {
  get(provider: PaymentGatewayProvider): IPaymentGateway;
}
