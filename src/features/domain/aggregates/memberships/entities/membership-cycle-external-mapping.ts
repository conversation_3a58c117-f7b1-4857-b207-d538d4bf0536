import {
  Entity,
  IEntity,
  EntityConstructorPayload,
} from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";

export type CreateMembershipCycleExternalMappingInput = {
  membershipCycleId: string;
  externalCycleId: string;
  provider: string;
};

export type UpdateMembershipCycleExternalMappingInput = {
  id: string;
  externalCycleId?: string;
  provider?: string;
};

export type MembershipCycleExternalMappingProps = {
  membershipCycleId: string;
  externalCycleId: string;
  provider: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type MembershipCycleExternalMappingMethods = {
  create(input: CreateMembershipCycleExternalMappingInput): Promise<void>;
  update(input: UpdateMembershipCycleExternalMappingInput): Promise<void>;
};

export type IMembershipCycleExternalMapping = IEntity<
  MembershipCycleExternalMappingProps,
  MembershipCycleExternalMappingMethods
>;

export class MembershipCycleExternalMapping
  extends Entity<
    MembershipCycleExternalMappingProps,
    MembershipCycleExternalMappingMethods
  >
  implements IMembershipCycleExternalMapping
{
  constructor(
    props?: EntityConstructorPayload<MembershipCycleExternalMappingProps>
  ) {
    super(props);
  }

  /** Props **/
  get membershipCycleId(): string {
    return this.props.membershipCycleId;
  }

  get externalCycleId(): string {
    return this.props.externalCycleId;
  }

  get provider(): string {
    return this.props.provider;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setMembershipCycleId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipCycleId", payload);
  }

  private setExternalCycleId(payload?: string) {
    if (payload !== undefined) this.setProp("externalCycleId", payload);
  }

  private setProvider(payload?: string) {
    if (payload !== undefined) this.setProp("provider", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateMembershipCycleExternalMappingInput) {
    // handle logic
    this.setId(randomUUID());
    this.setMembershipCycleId(payload.membershipCycleId);
    this.setExternalCycleId(payload.externalCycleId);
    this.setProvider(payload.provider);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateMembershipCycleExternalMappingInput) {
    // handle logic
    this.setId(payload.id);
    this.setExternalCycleId(payload.externalCycleId);
    this.setProvider(payload.provider);
    this.setUpdatedAt(new Date());
  }
}
