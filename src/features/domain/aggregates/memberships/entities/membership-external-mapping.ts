import {
  Entity,
  IEntity,
  EntityConstructorPayload,
} from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";

export type CreateMembershipExternalMappingInput = {
  membershipId: string;
  externalMembershipId: string;
  provider: string;
};

export type UpdateMembershipExternalMappingInput = {
  id: string;
  externalMembershipId?: string;
  provider?: string;
  updatedBy?: string;
};

export type MembershipExternalMappingProps = {
  membershipId: string;
  externalMembershipId: string;
  provider: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type MembershipExternalMappingMethods = {
  create(input: CreateMembershipExternalMappingInput): Promise<void>;
  update(input: UpdateMembershipExternalMappingInput): Promise<void>;
};

export type IMembershipExternalMapping = IEntity<
  MembershipExternalMappingProps,
  MembershipExternalMappingMethods
>;

export class MembershipExternalMapping
  extends Entity<
    MembershipExternalMappingProps,
    MembershipExternalMappingMethods
  >
  implements IMembershipExternalMapping
{
  constructor(
    props?: EntityConstructorPayload<MembershipExternalMappingProps>
  ) {
    super(props);
  }

  /** Props **/
  get membershipId(): string {
    return this.props.membershipId;
  }

  get externalMembershipId(): string {
    return this.props.externalMembershipId;
  }

  get provider(): string {
    return this.props.provider;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipId", payload);
  }

  private setExternalMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("externalMembershipId", payload);
  }

  private setProvider(payload?: string) {
    if (payload !== undefined) this.setProp("provider", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateMembershipExternalMappingInput) {
    // handle logic
    this.setId(randomUUID());
    this.setMembershipId(payload.membershipId);
    this.setExternalMembershipId(payload.externalMembershipId);
    this.setProvider(payload.provider);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateMembershipExternalMappingInput) {
    // handle logic
    this.setId(payload.id);
    this.setExternalMembershipId(payload.externalMembershipId);
    this.setProvider(payload.provider);
    this.setUpdatedAt(new Date());
  }
}
