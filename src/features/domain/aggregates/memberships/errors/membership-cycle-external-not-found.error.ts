import { RuntimeError } from "@heronjs/common";
import { Membership } from "@features/domain/aggregates/memberships/membership";
import { MembershipErrorCodes } from "@features/domain/aggregates/memberships/errors/errors";

export class MembershipCycleExternalNotFoundError extends RuntimeError {
  constructor() {
    super(
      Membership.AGGREGATE_NAME,
      MembershipErrorCodes.CYCLE_EXTERNAL_NOT_FOUND,
      "Membership cycle external mapping not found."
    );
  }
}
