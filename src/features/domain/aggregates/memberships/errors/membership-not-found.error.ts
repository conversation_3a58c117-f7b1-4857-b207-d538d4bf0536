import { MembershipErrorCodes } from "@features/domain/aggregates/memberships/errors/errors";
import { Membership } from "@features/domain/aggregates/memberships/membership";
import { RuntimeError } from "@heronjs/common";

export class MembershipNotFoundError extends RuntimeError {
  constructor() {
    super(
      Membership.AGGREGATE_NAME,
      MembershipErrorCodes.NOT_FOUND,
      "Not found"
    );
  }
}
