import { BillingCycleEnum } from "@features/domain/aggregates/memberships/enums";

export type CreateMembershipCycleInput = {
  membershipId: string;
  billingCycle: BillingCycleEnum;
  amount: number;
  createdBy: string;
};

export type UpdateMembershipCycleInput = {
  id: string;
  updatedBy?: string;
} & Partial<CreateMembershipCycleInput>;

export type UpsertMembershipCycleInput = Partial<UpdateMembershipCycleInput>;
