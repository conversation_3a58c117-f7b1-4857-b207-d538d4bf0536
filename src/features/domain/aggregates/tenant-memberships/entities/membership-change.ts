import {
  Entity,
  IEntity,
  EntityConstructorPayload,
} from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";

export type CreateMembershipChangeInput = {
  tenantMembershipId: string;
  startAt?: number; // timestamp
  expireAt: number; // timestamp
  endAt?: Nullable<number>; // timestamp
};

export type UpdateMembershipChangeInput = {
  id: string;
} & Partial<CreateMembershipChangeInput>;

export type MembershipChangeProps = {
  tenantMembershipId: string;
  startAt: number; // timestamp
  endAt: Nullable<number>; // timestamp
  expireAt: number; // timestamp
};

export type MembershipChangeMethods = {
  create(input: CreateMembershipChangeInput): Promise<void>;
  update(input: UpdateMembershipChangeInput): Promise<void>;
  end(endAt: number): Promise<void>;
};

export type IMembershipChange = IEntity<
  MembershipChangeProps,
  MembershipChangeMethods
>;

export class MembershipChange
  extends Entity<MembershipChangeProps, MembershipChangeMethods>
  implements IMembershipChange
{
  constructor(props?: EntityConstructorPayload<MembershipChangeProps>) {
    super(props);
  }

  /** Props **/

  get tenantMembershipId(): string {
    return this.props.tenantMembershipId;
  }

  get startAt(): number {
    return this.props.startAt;
  }

  get endAt(): Nullable<number> {
    return this.props.endAt;
  }

  get expireAt(): number {
    return this.props.expireAt;
  }

  /** Methods **/
  private setTenantMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("tenantMembershipId", payload);
  }

  private setStartAt(payload?: number) {
    if (payload !== undefined) this.setProp("startAt", payload);
  }

  private setEndAt(payload?: Nullable<number>) {
    if (payload !== undefined) this.setProp("endAt", payload);
  }

  private setExpireAt(payload?: number) {
    if (payload !== undefined) this.setProp("expireAt", payload);
  }

  async create(payload: CreateMembershipChangeInput): Promise<void> {
    this.setId(randomUUID());
    this.setTenantMembershipId(payload.tenantMembershipId);
    this.setStartAt(payload.startAt);
    this.setEndAt(payload.endAt);
    this.setExpireAt(payload.expireAt);
  }

  async update(payload: UpdateMembershipChangeInput): Promise<void> {
    this.setId(payload.id);
    this.setStartAt(payload.startAt);
    this.setEndAt(payload.endAt);
    this.setExpireAt(payload.expireAt);
  }

  async end(endAt: number): Promise<void> {
    this.setEndAt(endAt);
  }
}
