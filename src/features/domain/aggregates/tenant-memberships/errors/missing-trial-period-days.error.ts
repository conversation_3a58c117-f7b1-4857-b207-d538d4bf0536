import { RuntimeError } from "@heronjs/common";
import { TenantMembership } from "@features/domain/aggregates/tenant-memberships/tenant-membership";
import { TenantMembershipErrorCodes } from "@features/domain/aggregates/tenant-memberships/errors/errors";

export class MissingTrialPeriodDaysError extends RuntimeError {
  constructor() {
    super(
      TenantMembership.AGGREGATE_NAME,
      TenantMembershipErrorCodes.MISSING_TRIAL_PERIOD_DAYS,
      "Trial period days is required for trial memberships."
    );
  }
}
