import { RuntimeError } from "@heronjs/common";
import { TenantMembership } from "@features/domain/aggregates/tenant-memberships/tenant-membership";
import { TenantMembershipErrorCodes } from "@features/domain/aggregates/tenant-memberships/errors/errors";

export class CustomerIdIsNotProvidedError extends RuntimeError {
  constructor() {
    super(
      TenantMembership.AGGREGATE_NAME,
      TenantMembershipErrorCodes.CUSTOMER_ID_IS_NOT_PROVIDED,
      "Customer id is not provided."
    );
  }
}
