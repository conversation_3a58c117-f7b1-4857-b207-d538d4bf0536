import { RuntimeError } from "@heronjs/common";
import { TenantMembership } from "@features/domain/aggregates/tenant-memberships/tenant-membership";
import { TenantMembershipErrorCodes } from "@features/domain/aggregates/tenant-memberships/errors/errors";

export class UserNotFoundError extends RuntimeError {
  constructor() {
    super(
      TenantMembership.AGGREGATE_NAME,
      TenantMembershipErrorCodes.USER_NOT_FOUND,
      "User not found."
    );
  }
}
