import { RuntimeError } from "@heronjs/common";
import { Capability } from "@features/domain/aggregates/capabilities/capability";
import { CapabilityErrorCodes } from "@features/domain/aggregates/capabilities/errors/errors";

export class CapabilityNotFoundError extends RuntimeError {
  constructor() {
    super(
      Capability.AGGREGATE_NAME,
      CapabilityErrorCodes.NOT_FOUND,
      "Capability not found"
    );
  }
}
