import { RuntimeError } from "@heronjs/common";
import { CapabilityErrorCodes } from "./errors";
import { Capability } from "@features/domain/aggregates/capabilities/capability";

export class CapabilityKeyAlreadyExistsError extends RuntimeError {
  constructor() {
    super(
      Capability.AGGREGATE_NAME,
      CapabilityErrorCodes.KEY_ALREADY_EXISTS,
      "Capability with this key already exists"
    );
  }
}
