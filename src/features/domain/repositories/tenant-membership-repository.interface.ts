import {
  ITenantMembership,
  ITenantMembershipExternalMapping,
} from "@features/domain/aggregates";
import {
  IRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import { Nullable } from "@heronjs/common";

export interface ITenantMembershipRepository
  extends IRepository<ITenantMembership, RepositoryOptions> {
  createExternalMapping(
    entity: ITenantMembershipExternalMapping,
    options?: RepositoryOptions
  ): Promise<ITenantMembershipExternalMapping>;
  findOneExternalMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions
  ): Promise<Nullable<ITenantMembershipExternalMapping>>;
}
