import {
  QueryInput,
  IRepository,
  RepositoryOptions,
  QueryInputFindOne,
} from "@cbidigital/aqua-ddd";
import {
  IMembership,
  IMembershipCycle,
  IMembershipExternalMapping,
  IMembershipCycleExternalMapping,
} from "@features/domain/aggregates";
import { Nullable } from "@heronjs/common";

export interface IMembershipRepository
  extends IRepository<IMembership, RepositoryOptions> {
  upsertList(
    entities: IMembership[],
    options: RepositoryOptions
  ): Promise<IMembership[]>;

  count(
    input: Pick<QueryInput, "filter">,
    options?: RepositoryOptions
  ): Promise<number>;

  createExternalMapping(
    entity: IMembershipExternalMapping,
    options?: RepositoryOptions
  ): Promise<IMembershipExternalMapping>;

  findOneExternalMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions
  ): Promise<Nullable<IMembershipExternalMapping>>;

  findOneCycleExternalMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions
  ): Promise<Nullable<IMembershipCycleExternalMapping>>;

  createCycleExternalMapping(
    entity: IMembershipCycleExternalMapping,
    options?: RepositoryOptions
  ): Promise<IMembershipCycleExternalMapping>;

  findOneCycle(
    input: QueryInputFindOne,
    options?: RepositoryOptions
  ): Promise<Nullable<IMembershipCycle>>;
}
