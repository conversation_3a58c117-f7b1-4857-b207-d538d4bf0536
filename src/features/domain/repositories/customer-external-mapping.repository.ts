import { Nullable } from "@heronjs/common";
import {
  QueryInput,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import { ICustomerExternalMapping } from "@features/domain/aggregates";

export interface ICustomerExternalMappingRepository {
  create(
    entity: ICustomerExternalMapping,
    options?: RepositoryOptions
  ): Promise<ICustomerExternalMapping>;

  update(
    entity: ICustomerExternalMapping,
    options?: RepositoryOptions
  ): Promise<ICustomerExternalMapping>;

  findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions
  ): Promise<Nullable<ICustomerExternalMapping>>;

  find(
    input: QueryInput,
    options?: RepositoryOptions
  ): Promise<ICustomerExternalMapping[]>;

  findByCustomerId(
    customerId: string,
    options?: RepositoryOptions
  ): Promise<ICustomerExternalMapping[]>;

  findByCustomerIdAndProvider(
    customerId: string,
    provider: string,
    options?: RepositoryOptions
  ): Promise<Nullable<ICustomerExternalMapping>>;
}
