import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { CustomerExternalMappingDto } from "@features/domain/dtos";
import {
  CustomerExternalMapping,
  ICustomerExternalMapping,
} from "@features/domain/aggregates";

export type ICustomerExternalMappingMapper = IMapper<
  CustomerExternalMappingDto,
  ICustomerExternalMapping
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.CUSTOMER_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class CustomerExternalMappingMapper
  extends BaseMapper
  implements ICustomerExternalMappingMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(
    entity: ICustomerExternalMapping
  ): Promise<CustomerExternalMappingDto> {
    return {
      id: entity.id,
      customerId: entity.customerId,
      externalCustomerId: entity.externalCustomerId,
      provider: entity.provider,
      createdAt: entity.createdAt,
      createdBy: entity.createdBy,
      updatedAt: entity.updatedAt,
      updatedBy: entity.updatedBy,
    };
  }

  async fromDtoToEntity(
    dto: CustomerExternalMappingDto
  ): Promise<ICustomerExternalMapping> {
    return new CustomerExternalMapping({
      id: dto.id,
      props: {
        customerId: dto.customerId,
        externalCustomerId: dto.externalCustomerId,
        provider: dto.provider,
        createdAt: dto.createdAt,
        createdBy: dto.createdBy,
        updatedAt: dto.updatedAt,
        updatedBy: dto.updatedBy,
      },
    });
  }
}
