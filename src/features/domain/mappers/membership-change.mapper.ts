import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { MembershipChangeDto } from "@features/domain/dtos";
import {
  MembershipChange,
  IMembershipChange,
} from "@features/domain/aggregates";

export type IMembershipChangeMapper = IMapper<
  MembershipChangeDto,
  IMembershipChange
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CHANGE,
  scope: Lifecycle.Singleton,
})
export class MembershipChangeMapper
  extends BaseMapper
  implements IMembershipChangeMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(
    entity: IMembershipChange
  ): Promise<MembershipChangeDto> {
    return {
      id: entity.id,
      tenantMembershipId: entity.tenantMembershipId,
      startAt: entity.startAt,
      endAt: entity.endAt,
      expireAt: entity.expireAt,
    };
  }

  async fromDtoToEntity(dto: MembershipChangeDto): Promise<IMembershipChange> {
    return new MembershipChange({
      id: dto.id,
      props: {
        tenantMembershipId: dto.tenantMembershipId,
        startAt: dto.startAt,
        endAt: dto.endAt,
        expireAt: dto.expireAt,
      },
    });
  }
}
