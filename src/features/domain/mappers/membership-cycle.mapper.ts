import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { MembershipCycleDto } from "@features/domain/dtos";
import { IMembershipCycle, MembershipCycle } from "@features/domain/aggregates";
import { IMembershipCycleExternalMappingMapper } from "@features/domain/mappers/membership-cycle-external-mapping.mapper";

export type IMembershipCycleMapper = IMapper<
  MembershipCycleDto,
  IMembershipCycle
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE,
  scope: Lifecycle.Singleton,
})
export class MembershipCycleMapper
  extends BaseMapper
  implements IMembershipCycleMapper
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING)
    protected readonly cycleExternalMappingMapper: IMembershipCycleExternalMappingMapper
  ) {
    super();
  }

  async fromEntityToDto(entity: IMembershipCycle): Promise<MembershipCycleDto> {
    return {
      id: entity.id,
      membershipId: entity.membershipId,
      amount: entity.amount,
      billingCycle: entity.billingCycle,
      createdAt: entity.createdAt,
      createdBy: entity.createdBy,
      updatedAt: entity.updatedAt,
      updatedBy: entity.updatedBy,
    };
  }

  async fromDtoToEntity(dto: MembershipCycleDto): Promise<IMembershipCycle> {
    const externalMappings = dto.externalMappings
      ? await this.cycleExternalMappingMapper.fromDtosToEntities(
          dto.externalMappings
        )
      : null;
    return new MembershipCycle({
      id: dto.id,
      props: {
        membershipId: dto.membershipId,
        amount: dto.amount,
        billingCycle: dto.billingCycle,
        createdAt: dto.createdAt,
        createdBy: dto.createdBy,
        updatedAt: dto.updatedAt,
        updatedBy: dto.updatedBy,
        externalMappings,
      },
    });
  }
}
