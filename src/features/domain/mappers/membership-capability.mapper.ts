import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { MembershipCapabilityDto } from "@features/domain/dtos";
import {
  MembershipCapability,
  IMembershipCapability,
} from "@features/domain/aggregates/memberships/entities/membership-capability";

export type IMembershipCapabilityMapper = IMapper<
  MembershipCapabilityDto,
  IMembershipCapability
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CAPABILITY,
  scope: Lifecycle.Singleton,
})
export class MembershipCapabilityMapper
  extends BaseMapper
  implements IMembershipCapabilityMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(
    entity: IMembershipCapability
  ): Promise<MembershipCapabilityDto> {
    return {
      id: entity.id,
      membershipId: entity.membershipId,
      capabilityId: entity.capabilityId,
      value: entity.value,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(
    dto: MembershipCapabilityDto
  ): Promise<IMembershipCapability> {
    return new MembershipCapability({
      id: dto.id,
      props: {
        membershipId: dto.membershipId,
        capabilityId: dto.capabilityId,
        value: dto.value,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
