import {
  MembershipCycleExternalMapping,
  IMembershipCycleExternalMapping,
} from "@features/domain/aggregates";
import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { MembershipCycleExternalMappingDto } from "@features/domain";

export type IMembershipCycleExternalMappingMapper = IMapper<
  MembershipCycleExternalMappingDto,
  IMembershipCycleExternalMapping
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipCycleExternalMappingMapper
  extends BaseMapper
  implements IMembershipCycleExternalMappingMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(
    entity: IMembershipCycleExternalMapping
  ): Promise<MembershipCycleExternalMappingDto> {
    return {
      id: entity.id,
      membershipCycleId: entity.membershipCycleId,
      externalCycleId: entity.externalCycleId,
      provider: entity.provider,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(
    dto: MembershipCycleExternalMappingDto
  ): Promise<IMembershipCycleExternalMapping> {
    return new MembershipCycleExternalMapping({
      id: dto.id,
      props: {
        membershipCycleId: dto.membershipCycleId,
        externalCycleId: dto.externalCycleId,
        provider: dto.provider,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }

  async fromEntitiesToDtos(
    entities: IMembershipCycleExternalMapping[]
  ): Promise<MembershipCycleExternalMappingDto[]> {
    return Promise.all(entities.map((entity) => this.fromEntityToDto(entity)));
  }

  async fromDtosToEntities(
    dtos: MembershipCycleExternalMappingDto[]
  ): Promise<IMembershipCycleExternalMapping[]> {
    return Promise.all(dtos.map((dto) => this.fromDtoToEntity(dto)));
  }
}
