import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { TenantMembershipDto } from "@features/domain/dtos";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IMembershipChangeMapper } from "@features/domain/mappers/membership-change.mapper";
import { ITenantMembershipExternalMappingMapper } from "@features/domain/mappers/tenant-membership-external-mapping.mapper";
import {
  TenantMembership,
  ITenantMembership,
} from "@features/domain/aggregates";

export type ITenantMembershipMapper = IMapper<
  TenantMembershipDto,
  ITenantMembership
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipMapper
  extends Base<PERSON>apper
  implements ITenantMembershipMapper
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CHANGE)
    protected readonly membershipChangeMapper: IMembershipChangeMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP_EXTERNAL_MAPPING)
    protected readonly externalMappingMapper: ITenantMembershipExternalMappingMapper
  ) {
    super();
  }

  async fromEntityToDto(
    entity: ITenantMembership
  ): Promise<TenantMembershipDto> {
    const membershipChanges =
      await this.membershipChangeMapper.fromEntitiesToDtos(
        entity.membershipChanges
      );

    return {
      id: entity.id,
      tenantId: entity.tenantId,
      membershipCycleId: entity.membershipCycleId,
      status: entity.status,
      trialPeriodDays: entity.trialPeriodDays,
      createdAt: entity.createdAt,
      createdBy: entity.createdBy,
      updatedAt: entity.updatedAt,
      updatedBy: entity.updatedBy,
      membershipChanges,
    };
  }

  async fromDtoToEntity(dto: TenantMembershipDto): Promise<ITenantMembership> {
    // Map membership changes from DTO to entities
    const membershipChanges =
      await this.membershipChangeMapper.fromDtosToEntities(
        dto.membershipChanges
      );

    const externalTenantMembershipMappings =
      await this.externalMappingMapper.fromDtosToEntities(
        dto.externalTenantMembershipMappings ?? []
      );

    // Create the tenant membership entity with all properties including membership changes
    const tenantMembership = new TenantMembership({
      id: dto.id,
      props: {
        tenantId: dto.tenantId,
        membershipCycleId: dto.membershipCycleId,
        status: dto.status,
        trialPeriodDays: dto.trialPeriodDays,
        createdAt: dto.createdAt,
        createdBy: dto.createdBy,
        updatedAt: dto.updatedAt,
        updatedBy: dto.updatedBy,
        membershipChanges,
        externalTenantMembershipMappings,
      },
    });

    return tenantMembership;
  }
}
