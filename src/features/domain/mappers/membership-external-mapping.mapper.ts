import {
  MembershipExternalMapping,
  IMembershipExternalMapping,
} from "@features/domain/aggregates";
import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { MembershipExternalMappingDto } from "@features/domain";

export type IMembershipExternalMappingMapper = IMapper<
  MembershipExternalMappingDto,
  IMembershipExternalMapping
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipExternalMappingMapper
  extends BaseMapper
  implements IMembershipExternalMappingMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(
    entity: IMembershipExternalMapping
  ): Promise<MembershipExternalMappingDto> {
    return {
      id: entity.id,
      membershipId: entity.membershipId,
      externalMembershipId: entity.externalMembershipId,
      provider: entity.provider,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(
    dto: MembershipExternalMappingDto
  ): Promise<IMembershipExternalMapping> {
    return new MembershipExternalMapping({
      id: dto.id,
      props: {
        membershipId: dto.membershipId,
        externalMembershipId: dto.externalMembershipId,
        provider: dto.provider,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
