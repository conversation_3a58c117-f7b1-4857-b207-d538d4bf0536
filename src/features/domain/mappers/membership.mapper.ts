import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IMembership } from "@features/domain/aggregates";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IMembershipBuilder } from "@features/domain/builders";
import { MembershipCycleDto, MembershipDto } from "@features/domain/dtos";
import {
  IMembershipCycleMapper,
  IMembershipCapabilityMapper,
} from "@features/domain/mappers";

export type IMembershipMapper = IMapper<MembershipDto, IMembership>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class MembershipMapper extends BaseMapper implements IMembershipMapper {
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.MEMBERSHIP)
    protected readonly membershipBuilder: IMembershipBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE)
    protected readonly cycleMapper: IMembershipCycleMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CAPABILITY)
    protected readonly capabilityMapper: IMembershipCapabilityMapper
  ) {
    super();
  }

  async fromEntityToDto(entity: IMembership): Promise<MembershipDto> {
    const cycles = entity.cycles?.length
      ? await this.cycleMapper.fromEntitiesToDtos(entity.cycles)
      : [];
    const capabilities = entity.capabilities?.length
      ? await this.capabilityMapper.fromEntitiesToDtos(entity.capabilities)
      : [];

    const upsertedCycles = entity.upsertedCycles?.length
      ? await this.cycleMapper.fromEntitiesToDtos(entity.upsertedCycles)
      : [];
    const upsertedCapabilities = entity.upsertedCapabilities?.length
      ? await this.capabilityMapper.fromEntitiesToDtos(
          entity.upsertedCapabilities
        )
      : [];

    const deletedCycles = entity.deletedCycles?.length
      ? await this.cycleMapper.fromEntitiesToDtos(entity.deletedCycles)
      : [];
    const deletedCapabilities = entity.deletedCapabilities?.length
      ? await this.capabilityMapper.fromEntitiesToDtos(
          entity.deletedCapabilities
        )
      : [];
    const dto = {
      id: entity.id,
      name: entity.name,
      status: entity.status,
      isTrial: entity.isTrial,
      tenantId: entity.tenantId,
      isCustom: entity.isCustom,
      createdAt: entity.createdAt,
      createdBy: entity.createdBy,
      updatedAt: entity.updatedAt,
      updatedBy: entity.updatedBy,
      description: entity.description,
      cycles,
      capabilities,
      upsertedCycles,
      upsertedCapabilities,
      deletedCycles,
      deletedCapabilities,
    };

    return dto;
  }

  async fromDtoToEntity(dto: MembershipDto): Promise<IMembership> {
    return this.membershipBuilder.build({
      id: dto.id,
      props: {
        tenantId: dto.tenantId,
        name: dto.name,
        isCustom: dto.isCustom,
        isTrial: dto.isTrial,
        status: dto.status,
        createdAt: dto.createdAt,
        createdBy: dto.createdBy,
        updatedAt: dto.updatedAt,
        updatedBy: dto.updatedBy,
        description: dto.description,
        cycles: dto.cycles
          ? await Promise.all(
              dto.cycles.map((cycle) =>
                this.cycleMapper.fromDtoToEntity(cycle as MembershipCycleDto)
              )
            )
          : [],
        capabilities: dto.capabilities
          ? await Promise.all(
              dto.capabilities.map((capability) =>
                this.capabilityMapper.fromDtoToEntity(capability)
              )
            )
          : [],
      },
    });
  }
}
