import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { IWebhookHandlerFactory } from "@features/app/usecases/handlers";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import {
  EventPayload,
  IPaymentGatewayFactory,
  PaymentGatewayProvider,
} from "@features/domain";

export type HandleWebhookEventUseCaseInput = EventPayload & {
  provider: PaymentGatewayProvider;
};
export type HandleWebhookEventUseCaseOutput = void;

export type IHandleWebhookEventUseCase = IUseCase<
  HandleWebhookEventUseCaseInput,
  HandleWebhookEventUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.HANDLE_WEBHOOK_EVENT,
  scope: Lifecycle.Transient,
})
export class HandleWebhookEventUseCase
  extends UseCase<
    HandleWebhookEventUseCaseInput,
    HandleWebhookEventUseCaseOutput,
    UseCaseContext
  >
  implements IHandleWebhookEventUseCase
{
  private readonly logger: ILogger;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.WEBHOOK_HANDLER)
    protected readonly webhookHandlerFactory: IWebhookHandlerFactory
  ) {
    super();
    this.setMethods(this.processing);
    this.logger = new Logger(this.constructor.name);
  }

  processing = async (input: HandleWebhookEventUseCaseInput) => {
    try {
      const paymentGateway = this.paymentGatewayFactory.get(input.provider);
      const parsedEvent = await paymentGateway.parseEvent(input);
      const webhookHandler = this.webhookHandlerFactory.get(parsedEvent.type);
      if (!webhookHandler) throw new Error("Webhook handler not found");
      await webhookHandler.handle(parsedEvent);
    } catch (error) {
      this.logger.error("Failed to handle webhook event", error);
      throw error;
    }
  };
}
