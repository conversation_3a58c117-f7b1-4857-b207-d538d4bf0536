import z from "zod";
import { PAYMENT_GATEWAY_CONFIG } from "@configs";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IPaymentService } from "@features/app/services";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import {
  UseCase,
  IUseCase,
  UseCaseContext,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  IDatabaseUtil,
  IPaymentGateway,
  ITenantMembership,
  CustomerNotFoundError,
  IMembershipRepository,
  IPaymentGatewayFactory,
  ITenantMembershipBuilder,
  PaymentGatewaySubscription,
  ITenantMembershipRepository,
  CustomerIdIsNotProvidedError,
  MembershipCycleNotFoundError,
  TenantMembershipNotFoundError,
  TenantMembershipExternalMapping,
  MembershipCycleExternalNotFoundError,
} from "@features/domain";

export type ActivateExternalSubscriptionUseCaseInput = {
  tenantMembershipId: string;
};

export type ActivateExternalSubscriptionUseCaseOutput = void;

const ActivateExternalSubscriptionUseCaseInputSchema = z.object({
  tenantMembershipId: z.string(),
});

export type IActivateExternalSubscriptionUseCase = IUseCase<
  ActivateExternalSubscriptionUseCaseInput,
  ActivateExternalSubscriptionUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.ACTIVATE_EXTERNAL_SUBSCRIPTION,
  scope: Lifecycle.Transient,
})
export class ActivateExternalSubscriptionUseCase
  extends UseCase<
    ActivateExternalSubscriptionUseCaseInput,
    ActivateExternalSubscriptionUseCaseOutput,
    UseCaseContext
  >
  implements IActivateExternalSubscriptionUseCase
{
  private readonly paymentGateway: IPaymentGateway;
  private readonly logger: ILogger;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.TENANT_MEMBERSHIP)
    protected readonly builder: ITenantMembershipBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.SERVICE.PAYMENT)
    protected readonly paymentService: IPaymentService,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository
  ) {
    super();
    this.setMethods(this.processing);
    this.logger = new Logger(this.constructor.name);
    this.paymentGateway = this.paymentGatewayFactory.get(
      PAYMENT_GATEWAY_CONFIG.PROVIDER
    );
  }

  processing = async (input: ActivateExternalSubscriptionUseCaseInput) => {
    const model = ActivateExternalSubscriptionUseCaseInputSchema.parse(input);
    const customerId = this.context.auth?.authId;
    if (!customerId) throw new CustomerIdIsNotProvidedError();
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const [tenantMembership, customerInfo] = await Promise.all([
        this.tenantMembershipRepo.findOne({
          filter: { id: { $eq: model.tenantMembershipId } },
        }),
        this.paymentService.getExternalCustomer(customerId),
      ]);
      if (!tenantMembership) throw new TenantMembershipNotFoundError();
      if (!customerInfo) throw new CustomerNotFoundError();
      const membershipCycle = await this.membershipRepo.findOneCycle({
        filter: { id: { $eq: tenantMembership.membershipCycleId } },
      });
      if (!membershipCycle) throw new MembershipCycleNotFoundError();
      const externalMembershipCycle = membershipCycle.getExternalMapping(
        PAYMENT_GATEWAY_CONFIG.PROVIDER
      );
      if (!externalMembershipCycle)
        throw new MembershipCycleExternalNotFoundError();

      // Create subscription
      const subscription = await this.paymentGateway.createSubscription({
        customerId: customerInfo.gatewayCustomerId,
        priceId: externalMembershipCycle.externalCycleId,
        metadata: {
          tenantMembershipId: tenantMembership.id,
        },
      });

      // Create tenant membership mapping
      await this.createTenantMembershipMapping(
        { tenantMembership, subscription, customerInfo },
        repoOptions
      );
      await trx.commit();
      this.logger.info("Subscription created successfully", subscription);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  };

  private createTenantMembershipMapping = async (
    input: {
      tenantMembership: ITenantMembership;
      subscription: PaymentGatewaySubscription;
      customerInfo: any;
    },
    repoOptions: RepositoryOptions
  ) => {
    const tenantMembershipMapping = new TenantMembershipExternalMapping();
    await tenantMembershipMapping.create({
      provider: PAYMENT_GATEWAY_CONFIG.PROVIDER,
      tenantMembershipId: input.tenantMembership.id,
      externalTenantMembershipId: input.subscription.id,
      externalCustomerId: input.customerInfo.id,
    });
    await this.tenantMembershipRepo.createExternalMapping(
      tenantMembershipMapping,
      repoOptions
    );
  };
}
