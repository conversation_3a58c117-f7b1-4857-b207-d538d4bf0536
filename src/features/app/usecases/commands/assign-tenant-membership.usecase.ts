import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import {
  Inject,
  Lifecycle,
  Provider,
  Logger,
  RuntimeError,
} from "@heronjs/common";
import {
  UseCase,
  IUseCase,
  UseCaseContext,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  IDatabaseUtil,
  ITenantMembership,
  IMembershipRepository,
  ITenantMembershipBuilder,
  SameMembershipCycleError,
  TenantMembershipStatusEnum,
  ITenantMembershipRepository,
  CreateTenantMembershipInput,
  MissingTrialPeriodDaysError,
} from "@features/domain";

export type AssignTenantMembershipUseCaseInput = CreateTenantMembershipInput;

export type AssignTenantMembershipUseCaseOutput = { id: string };

const AssignTenantMembershipUseCaseInputSchema = z.object({
  tenantId: z.string(),
  customerId: z.string().optional(),
  membershipCycleId: z.string(),
  isTrial: z.boolean(),
  trialPeriodDays: z.number().optional(),
  createdBy: z.string(),
});

export type IAssignTenantMembershipUseCase = IUseCase<
  AssignTenantMembershipUseCaseInput,
  AssignTenantMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.ASSIGN_TENANT_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class AssignTenantMembershipUseCase
  extends UseCase<
    AssignTenantMembershipUseCaseInput,
    AssignTenantMembershipUseCaseOutput,
    UseCaseContext
  >
  implements IAssignTenantMembershipUseCase
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.TENANT_MEMBERSHIP)
    protected readonly builder: ITenantMembershipBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: AssignTenantMembershipUseCaseInput) => {
    const model = AssignTenantMembershipUseCaseInputSchema.parse(input);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };

    try {
      // Handle current tenant membership
      await this.handleCurrentTenantMembership(model, repoOptions);

      // Create new tenant membership
      const entity = await this.createTenantMembership(model, repoOptions);
      await trx.commit();
      return { id: entity.id };
    } catch (error) {
      this.logger.error("Failed to assign tenant membership", error);
      await trx.rollback();
      throw error;
    }
  };

  /**
   * Handle current tenant membership
   */
  private async handleCurrentTenantMembership(
    model: AssignTenantMembershipUseCaseInput,
    repoOptions: RepositoryOptions
  ): Promise<void> {
    const currentTenantMembership = await this.tenantMembershipRepo.findOne(
      {
        filter: {
          tenantId: { $eq: model.tenantId },
          isActive: { $eq: true },
        },
      },
      repoOptions
    );

    if (currentTenantMembership)
      throw new RuntimeError();
  }

  /**
   * Create a new tenant membership
   */
  private async createTenantMembership(
    model: AssignTenantMembershipUseCaseInput,
    repoOptions: RepositoryOptions
  ): Promise<ITenantMembership> {
    // Create tenant membership
    const entity = await this.builder.build();
    await entity.create(model);
    const isMissingTrialPeriodDays =
      entity.status === TenantMembershipStatusEnum.TRIALING &&
      !entity.trialPeriodDays;
    if (isMissingTrialPeriodDays) throw new MissingTrialPeriodDaysError();

    // Create membership change record
    if (
      entity.status === TenantMembershipStatusEnum.TRIALING &&
      entity.trialPeriodDays
    ) {
      // Calculate expiration date based on trial period days
      const DAY_IN_MS = 24 * 60 * 60 * 1000;
      const currDateInMs = Date.now();
      await entity.addMembershipChange({
        startAt: currDateInMs,
        expireAt: currDateInMs + entity.trialPeriodDays * DAY_IN_MS,
      });
    }

    // Save tenant membership with its membership changes
    await this.tenantMembershipRepo.create(entity, repoOptions);

    return entity;
  }
}
