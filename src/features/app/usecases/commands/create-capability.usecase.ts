import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  ICapabilityBuilder,
  ICapabilityRepository,
  CreateCapabilityInput,
  CapabilityKeyAlreadyExistsError,
} from "@features/domain";

export type CreateCapabilityUseCaseInput = CreateCapabilityInput;
export type CreateCapabilityUseCaseOutput = { id: string };

const CreateCapabilityUseCaseInputSchema = z.object({
  key: z.string(),
  description: z.string(),
});

export type ICreateCapabilityUseCase = IUseCase<
  CreateCapabilityUseCaseInput,
  CreateCapabilityUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.CREATE_CAPABILITY,
  scope: Lifecycle.Transient,
})
export class CreateCapabilityUseCase
  extends UseCase<
    CreateCapabilityUseCaseInput,
    CreateCapabilityUseCaseOutput,
    UseCaseContext
  >
  implements ICreateCapabilityUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.CAPABILITY)
    protected readonly builder: ICapabilityBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.CAPABILITY)
    protected readonly repo: ICapabilityRepository
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: CreateCapabilityUseCaseInput) => {
    const { key, description } =
      CreateCapabilityUseCaseInputSchema.parse(input);

    // Check if capability with the same key already exists
    const existingCapability = await this.repo.findByKey(key);
    if (existingCapability) throw new CapabilityKeyAlreadyExistsError();

    // Create new capability
    const capability = await this.builder.build();
    await capability.create({ key, description });
    await this.repo.create(capability);

    return { id: capability.id };
  };
}
