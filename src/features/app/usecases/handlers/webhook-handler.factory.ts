import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_EVENT_TYPE, PaymentEvent } from "@features/domain";

export interface IWebhookHandler {
  handle(event: PaymentEvent): Promise<void>;
}

export interface IWebhookHandlerFactory {
  get(type: string): IWebhookHandler;
}

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.FACTORY.WEBHOOK_HANDLER,
  scope: Lifecycle.Singleton,
})
export class WebhookHandlerFactory implements IWebhookHandlerFactory {
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.HANDLER.PAYMENT_SUCCEEDED)
    private readonly paymentSucceededHandler: IWebhookHandler
  ) {}

  get(type: string) {
    let handler = null;
    if (PAYMENT_EVENT_TYPE.PAYMENT_SUCCEEDED.includes(type)) {
      handler = this.paymentSucceededHandler;
    }
    if (!handler) throw new Error("Webhook handler not found");
    return handler;
  }
}
