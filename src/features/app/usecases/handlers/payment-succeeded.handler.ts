import {
  Subscription,
  IDatabaseUtil,
  IPaymentGateway,
  ITenantMembership,
  IMembershipRepository,
  PaymentSucceededEvent,
  SubscriptionFoundError,
  IPaymentGatewayFactory,
  ITenantMembershipRepository,
  TenantMembershipNotFoundError,
  MissingTenantMembershipIdError,
} from "@features/domain";
import { PAYMENT_GATEWAY_CONFIG } from "@configs";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { RepositoryOptions } from "@cbidigital/aqua-ddd";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhookHandler } from "@features/app/usecases/handlers/webhook-handler.factory";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.HANDLER.PAYMENT_SUCCEEDED,
  scope: Lifecycle.Singleton,
})
export class PaymentSucceeded<PERSON>andler implements IWebhookHandler {
  private readonly logger: ILogger;
  private readonly paymentGateway: IPaymentGateway;

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory
  ) {
    this.logger = new Logger(this.constructor.name);
    this.paymentGateway = this.paymentGatewayFactory.get(
      PAYMENT_GATEWAY_CONFIG.PROVIDER
    );
  }

  async handle(event: PaymentSucceededEvent) {
    const { data } = event;
    const { tenantMembershipId } = data.metadata;
    if (!tenantMembershipId) throw new MissingTenantMembershipIdError();
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      // Handle subscription created
      const { tenantMembership, subscription } = await this.fetchSubscription(
        event,
        repoOptions
      );
      await this.endCurrentMembershipChange(tenantMembership);
      await this.addNewMembershipChange(tenantMembership, subscription);
      await tenantMembership.onPaymentSucceeded();
      await this.tenantMembershipRepo.update(tenantMembership, repoOptions);
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  private async fetchSubscription(
    event: PaymentSucceededEvent,
    repoOptions: RepositoryOptions
  ) {
    const { tenantMembershipId } = event.data.metadata;
    const [tenantMembership, subscription] = await Promise.all([
      this.tenantMembershipRepo.findOne(
        { filter: { id: { $eq: tenantMembershipId } } },
        repoOptions
      ),
      this.paymentGateway.getSubscription(event.data.subscription),
    ]);
    if (!tenantMembership) throw new TenantMembershipNotFoundError();
    if (!subscription) throw new SubscriptionFoundError();
    return { tenantMembership, subscription };
  }

  private async endCurrentMembershipChange(
    tenantMembership: ITenantMembership
  ) {
    const membershipChange = tenantMembership.getCurrentMembershipChange();
    if (membershipChange) {
      const CURRENT_DAY_IN_SEC = Math.floor(Date.now() / 1000);
      await membershipChange.end(CURRENT_DAY_IN_SEC);
    }
  }

  private async addNewMembershipChange(
    tenantMembership: ITenantMembership,
    subscription: Subscription
  ) {
    const { startAt, expireAt } = subscription;
    await tenantMembership.addMembershipChange({ startAt, expireAt });
  }
}
