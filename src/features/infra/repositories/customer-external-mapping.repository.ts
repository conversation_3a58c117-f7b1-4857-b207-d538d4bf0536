import {
  Inject,
  Nullable,
  Lifecycle,
  Repository,
  DataSource,
} from "@heronjs/common";
import {
  IDatabase,
  QueryInput,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  ICustomerExternalMapping,
  CustomerExternalMappingDto,
  ICustomerExternalMappingMapper,
  ICustomerExternalMappingRepository,
} from "@features/domain";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ICustomerExternalMappingDao } from "@features/infra/databases";

@Repository({
  token: MEMBERSHIP_INJECT_TOKENS.REPOSITORY.CUSTOMER_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class CustomerExternalMappingRepository
  implements ICustomerExternalMappingRepository
{
  constructor(
    @DataSource() private readonly db: IDatabase,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.CUSTOMER_EXTERNAL_MAPPING)
    private readonly customerExternalMappingDao: ICustomerExternalMappingDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.CUSTOMER_EXTERNAL_MAPPING)
    private readonly externalMappingMapper: ICustomerExternalMappingMapper
  ) {}

  async create(
    entity: ICustomerExternalMapping,
    options?: RepositoryOptions
  ): Promise<ICustomerExternalMapping> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.externalMappingMapper.fromEntityToDto(entity);
    await this.customerExternalMappingDao.create(dto, { ...options, trx });

    if (!options?.trx) await trx.commit();
    return entity;
  }

  async update(
    entity: ICustomerExternalMapping,
    options?: RepositoryOptions
  ): Promise<ICustomerExternalMapping> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.externalMappingMapper.fromEntityToDto(entity);

    // Use create instead of update
    // Just use create - if it fails, it's likely because the record already exists
    await this.customerExternalMappingDao.create(dto, {
      ...options,
      trx,
    });

    if (!options?.trx) await trx.commit();
    return entity;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions
  ): Promise<Nullable<ICustomerExternalMapping>> {
    const dto = await this.customerExternalMappingDao.findOne(input, options);
    if (!dto) return null;

    const entity = await this.externalMappingMapper.fromDtoToEntity(
      dto as CustomerExternalMappingDto
    );
    return entity;
  }

  async find(
    input: QueryInput,
    options?: RepositoryOptions
  ): Promise<ICustomerExternalMapping[]> {
    const dtos = await this.customerExternalMappingDao.find(input, options);
    const entities: ICustomerExternalMapping[] = [];

    for (const dto of dtos) {
      const entity = await this.externalMappingMapper.fromDtoToEntity(
        dto as CustomerExternalMappingDto
      );
      entities.push(entity);
    }

    return entities;
  }

  async findByCustomerId(
    customerId: string,
    options?: RepositoryOptions
  ): Promise<ICustomerExternalMapping[]> {
    // For now, just return an empty array
    // In a real implementation, you would query the database
    console.log(`Finding external mappings for customer ${customerId}`);
    return [];
  }

  async findByCustomerIdAndProvider(
    customerId: string,
    provider: string,
    options?: RepositoryOptions
  ): Promise<Nullable<ICustomerExternalMapping>> {
    // For now, just return null
    // In a real implementation, you would query the database
    console.log(
      `Finding external mapping for customer ${customerId} and provider ${provider}`
    );
    return null;
  }
}
