import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Lifecycle, Provider } from "@heronjs/common";
import { IRetryUtil } from "@features/infra/utils/retry/retry.util";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.UTIL.RETRY,
  scope: Lifecycle.Singleton,
})
export class BasicRetryUtil implements IRetryUtil {
  async retry<T>(
    fn: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
  ): Promise<T> {
    let retryCount = 0;
    while (retryCount < maxRetries) {
      try {
        return await fn();
      } catch (error) {
        retryCount++;
        if (retryCount < maxRetries) {
          await new Promise((res) => setTimeout(res, delay));
        } else {
          throw error;
        }
      }
    }
    throw new Error("Max retries reached");
  }
}
