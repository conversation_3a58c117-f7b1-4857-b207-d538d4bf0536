import { Filter, FilterPayload, FilterTypes } from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";

export class TenantMembershipExternalMappingIdFilter extends Filter {
  constructor(payload: FilterPayload) {
    super({
      type: FilterTypes.STRING,
      compareField: `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING}.id`,
      ...payload,
    });
  }
}
