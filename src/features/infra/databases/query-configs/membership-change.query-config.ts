import { QueryConfig } from "@cbidigital/aqua-ddd";
import { MembershipChangeDto } from "@features/domain";

export const MembershipChangeQueryConfig: QueryConfig<
  keyof MembershipChangeDto
> = {
  id: {
    sortable: true,
    filterable: true,
  },
  startAt: {
    sortable: true,
    filterable: true,
  },
  endAt: {
    sortable: true,
    filterable: true,
  },
  expireAt: {
    sortable: true,
    filterable: true,
  },
};
