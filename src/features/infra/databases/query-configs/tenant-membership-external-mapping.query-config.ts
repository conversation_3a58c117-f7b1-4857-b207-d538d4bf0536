import { QueryConfig } from "@cbidigital/aqua-ddd";
import { TenantMembershipExternalMappingDto } from "@features/domain";
import {
  TenantIdFilter,
  TenantMembershipIdFilter,
} from "@features/infra/databases/filters";

export const TenantMembershipExternalMappingQueryConfig: QueryConfig<
  keyof TenantMembershipExternalMappingDto
> = {
  id: {
    sortable: true,
    filterable: true,
    filterClass: TenantMembershipIdFilter,
  },

  tenantMembershipId: {
    sortable: true,
    filterable: true,
    filterClass: TenantIdFilter,
  },
};
