import { <PERSON><PERSON><PERSON>ordMapper } from "@cbidigital/aqua-ddd";
import { CustomerExternalMappingDto } from "@features/domain";
import { CustomerExternalMappingRecord } from "@features/infra/databases/records";

export class CustomerExternalMappingRecordMapper
  implements
    IRecordMapper<CustomerExternalMappingDto, CustomerExternalMappingRecord>
{
  fromRecordToDto(
    record: CustomerExternalMappingRecord
  ): CustomerExternalMappingDto {
    return {
      id: record.id,
      customerId: record.customer_id,
      externalCustomerId: record.external_customer_id,
      provider: record.provider,
      createdAt: record.created_at,
      createdBy: record.created_by,
      updatedAt: record.updated_at,
      updatedBy: record.updated_by,
    };
  }

  fromDtoToRecord(
    domain: CustomerExternalMappingDto
  ): CustomerExternalMappingRecord {
    return {
      id: domain.id,
      customer_id: domain.customerId,
      external_customer_id: domain.externalCustomerId,
      provider: domain.provider,
      created_at: domain.createdAt,
      created_by: domain.createdBy,
      updated_at: domain.updatedAt,
      updated_by: domain.updatedBy,
    };
  }

  fromRecordsToDtos(
    records: CustomerExternalMappingRecord[]
  ): CustomerExternalMappingDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(
    dtos: CustomerExternalMappingDto[]
  ): CustomerExternalMappingRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
