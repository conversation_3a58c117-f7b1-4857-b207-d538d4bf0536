import {
  MembershipCycleDto,
  MembershipCycleExternalMappingDto,
} from "@features/domain";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import { MembershipCycleRecord } from "@features/infra/databases/records";
import { MembershipCycleExternalMappingRecordMapper } from "@features/infra/databases/record-mappers/membership-cycle-external-mapping.record-mapper";

export type IMembershipCycleRecordMapper = IRecordMapper<
  MembershipCycleDto,
  MembershipCycleRecord
>;
export class MembershipCycleRecordMapper
  extends BaseRecordMapper
  implements IMembershipCycleRecordMapper
{
  private readonly externalMappingMapper =
    new MembershipCycleExternalMappingRecordMapper();

  fromRecordToDto(
    record: Partial<MembershipCycleRecord>
  ): Partial<MembershipCycleDto> {
    const externalMappings = record.external_mappings
      ? this.externalMappingMapper.fromRecordsToDtos(record.external_mappings)
      : undefined;
    return {
      id: record.id,
      membershipId: record.membership_id,
      amount: record.amount,
      billingCycle: record.billing_cycle,
      createdAt: record.created_at,
      createdBy: record.created_by,
      updatedAt: record.updated_at,
      updatedBy: record.updated_by,
      externalMappings: externalMappings as MembershipCycleExternalMappingDto[],
    };
  }

  fromDtoToRecord(
    dto: Partial<MembershipCycleDto>
  ): Partial<MembershipCycleRecord> {
    return {
      id: dto.id,
      membership_id: dto.membershipId,
      amount: dto.amount,
      billing_cycle: dto.billingCycle,
      created_at: dto.createdAt,
      created_by: dto.createdBy,
      updated_at: dto.updatedAt,
      updated_by: dto.updatedBy,
    };
  }
}
