import { MembershipExternalMappingDto } from "@features/domain";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import { MembershipExternalMappingRecord } from "@features/infra/databases/records";

export type IMembershipExternalMappingRecordMapper = IRecordMapper<
  MembershipExternalMappingDto,
  MembershipExternalMappingRecord
>;

export class MembershipExternalMappingRecordMapper
  extends BaseRecordMapper
  implements IMembershipExternalMappingRecordMapper
{
  constructor() {
    super();
  }

  fromRecordToDto(
    record: Partial<MembershipExternalMappingRecord>
  ): Partial<MembershipExternalMappingDto> {
    const dto: Partial<MembershipExternalMappingDto> = {
      id: record.id,
      membershipId: record.membership_id,
      externalMembershipId: record.external_membership_id,
      provider: record.provider,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
    return dto;
  }

  fromDtoToRecord(
    dto: Partial<MembershipExternalMappingDto>
  ): Partial<MembershipExternalMappingRecord> {
    return {
      id: dto.id,
      membership_id: dto.membershipId,
      external_membership_id: dto.externalMembershipId,
      provider: dto.provider,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }
}
