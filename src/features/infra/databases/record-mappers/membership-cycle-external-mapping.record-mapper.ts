import { MembershipCycleExternalMappingDto } from "@features/domain";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import { MembershipCycleExternalMappingRecord } from "@features/infra/databases/records";

export type IMembershipCycleExternalMappingRecordMapper = IRecordMapper<
  MembershipCycleExternalMappingDto,
  MembershipCycleExternalMappingRecord
>;

export class MembershipCycleExternalMappingRecordMapper
  extends BaseRecordMapper
  implements IMembershipCycleExternalMappingRecordMapper
{
  fromRecordToDto(
    record: Partial<MembershipCycleExternalMappingRecord>
  ): Partial<MembershipCycleExternalMappingDto> {
    return {
      id: record.id,
      membershipCycleId: record.membership_cycle_id,
      externalCycleId: record.external_cycle_id,
      provider: record.provider,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(
    dto: Partial<MembershipCycleExternalMappingDto>
  ): Partial<MembershipCycleExternalMappingRecord> {
    return {
      id: dto.id,
      membership_cycle_id: dto.membershipCycleId,
      external_cycle_id: dto.externalCycleId,
      provider: dto.provider,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(
    records: Partial<MembershipCycleExternalMappingRecord>[]
  ): Partial<MembershipCycleExternalMappingDto>[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(
    dtos: Partial<MembershipCycleExternalMappingDto>[]
  ): Partial<MembershipCycleExternalMappingRecord>[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
