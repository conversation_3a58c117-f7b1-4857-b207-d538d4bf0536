import { TenantMembershipExternalMappingDto } from "@features/domain";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import { TenantMembershipExternalMappingRecord } from "@features/infra/databases/records";

export type ITenantMembershipExternalMappingRecordMapper = IRecordMapper<
  TenantMembershipExternalMappingDto,
  TenantMembershipExternalMappingRecord
>;

export class TenantMembershipExternalMappingRecordMapper
  extends BaseRecordMapper
  implements ITenantMembershipExternalMappingRecordMapper
{
  constructor() {
    super();
  }

  fromRecordToDto(
    record: Partial<TenantMembershipExternalMappingRecord>
  ): Partial<TenantMembershipExternalMappingDto> {
    const dto: Partial<TenantMembershipExternalMappingDto> = {
      id: record.id,
      tenantMembershipId: record.tenant_membership_id,
      externalTenantMembershipId: record.external_tenant_membership_id,
      externalCustomerId: record.external_customer_id,
      provider: record.provider,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
    return dto;
  }

  fromDtoToRecord(
    dto: Partial<TenantMembershipExternalMappingDto>
  ): Partial<TenantMembershipExternalMappingRecord> {
    return {
      id: dto.id,
      tenant_membership_id: dto.tenantMembershipId,
      external_tenant_membership_id: dto.externalTenantMembershipId,
      external_customer_id: dto.externalCustomerId,
      provider: dto.provider,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }
}
