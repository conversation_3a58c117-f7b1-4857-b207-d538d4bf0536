import { MembershipCycleDto, MembershipDto } from "@features/domain";
import { MembershipRecord } from "@features/infra/databases/records";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import {
  MembershipCycleRecordMapper,
  IMembershipCycleRecordMapper,
  MembershipCapabilityRecordMapper,
} from "@features/infra/databases/record-mappers";

export type IMembershipRecordMapper = IRecordMapper<
  MembershipDto,
  MembershipRecord
>;
export class MembershipRecordMapper
  extends BaseRecordMapper
  implements IMembershipRecordMapper
{
  private readonly cycleMapper: IMembershipCycleRecordMapper;
  private readonly capabilityMapper: MembershipCapabilityRecordMapper;

  constructor() {
    super();
    this.cycleMapper = new MembershipCycleRecordMapper();
    this.capabilityMapper = new MembershipCapabilityRecordMapper();
  }

  fromRecordToDto(record: Partial<MembershipRecord>): Partial<MembershipDto> {
    const cycles = record.cycles?.length
      ? this.cycleMapper.fromRecordsToDtos(record.cycles)
      : [];
    const capabilities = record.capabilities?.length
      ? this.capabilityMapper.fromRecordsToDtos(record.capabilities)
      : [];

    const dto: Partial<MembershipDto> = {
      id: record.id,
      tenantId: record.tenant_id,
      name: record.name,
      status: record.status,
      isTrial: record.is_trial,
      isCustom: record.is_custom,
      description: record.description,
      cycles,
      capabilities,
      createdAt: record.created_at,
      createdBy: record.created_by,
      updatedAt: record.updated_at,
      updatedBy: record.updated_by,
    };
    return dto;
  }

  fromDtoToRecord(dto: Partial<MembershipDto>): Partial<MembershipRecord> {
    return {
      id: dto.id,
      tenant_id: dto.tenantId,
      name: dto.name,
      status: dto.status,
      is_trial: dto.isTrial,
      is_custom: dto.isCustom,
      created_at: dto.createdAt,
      created_by: dto.createdBy,
      updated_at: dto.updatedAt,
      updated_by: dto.updatedBy,
      description: dto.description,
    };
  }
}
