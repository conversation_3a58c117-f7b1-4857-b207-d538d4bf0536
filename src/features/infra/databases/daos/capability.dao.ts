import { <PERSON><PERSON> } from "knex";
import { CapabilityDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { CapabilityRecord } from "@features/infra/databases/records";
import { CapabilityRecordMapper } from "@features/infra/databases/record-mappers";
import {
  BaseDao,
  IBaseDao,
  IDatabase,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_TABLE_NAMES,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
} from "@constants";

export interface ICapabilityDao
  extends IBaseDao<CapabilityDto, CapabilityRecord> {
  update(
    entity: Partial<CapabilityDto>,
    options?: RepositoryOptions
  ): Promise<Partial<CapabilityDto>>;
}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.CAPABILITY,
  scope: Lifecycle.Singleton,
})
export class CapabilityDao
  extends BaseDao<CapabilityDto, CapabilityRecord>
  implements ICapabilityDao
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.CAPABILITY,
      recordMapper: new CapabilityRecordMapper(),
    });
  }

  async update(
    dto: Partial<CapabilityDto>,
    options: RepositoryOptions
  ): Promise<Partial<CapabilityDto>> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client
      .table(this.tableName)
      .where("id", dto.id!)
      .update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }
}
