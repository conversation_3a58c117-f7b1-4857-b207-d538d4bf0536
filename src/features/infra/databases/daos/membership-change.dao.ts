import { <PERSON><PERSON> } from "knex";
import { MembershipChangeDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { MembershipChangeRecord } from "@features/infra/databases/records";
import { MembershipChangeRecordMapper } from "@features/infra/databases/record-mappers";
import { MembershipChangeQueryConfig } from "@features/infra/databases/query-configs";
import {
  BaseDao,
  DaoUtils,
  IBaseDao,
  IDatabase,
  QueryInput,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_TABLE_NAMES,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
} from "@constants";

export interface IMembershipChangeDao
  extends IBaseDao<MembershipChangeDto, MembershipChangeRecord> {
  update(
    entity: Partial<MembershipChangeDto>,
    options?: RepositoryOptions
  ): Promise<Partial<MembershipChangeDto>>;
}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CHANGE,
  scope: Lifecycle.Singleton,
})
export class MembershipChangeDao
  extends BaseDao<MembershipChangeDto, MembershipChangeRecord>
  implements IMembershipChangeDao
{
  private readonly logger = new Logger(this.constructor.name);
  private readonly mergedColumns = [
    "tenant_membership_id",
    "start_at",
    "end_at",
    "expire_at",
  ];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CHANGE,
      recordMapper: new MembershipChangeRecordMapper(),
    });
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    const query = builder.select("*");
    return query;
  }

  async find(
    payload: QueryInput & { search?: string },
    options?: RepositoryOptions
  ): Promise<Partial<MembershipChangeDto>[]> {
    const { offset, limit, sort, filter, search } = payload;
    const client = this.db.getClient(options?.tenantId);
    const query = client.from(this.tableName).modify(this.buildSelectClause);
    if (search) this.applySearch(query, search);
    if (filter)
      DaoUtils.applyFilter(filter, MembershipChangeQueryConfig, query);
    if (offset !== undefined) query.offset(offset);
    if (limit !== undefined) query.limit(limit);
    if (sort) DaoUtils.applySort(sort, MembershipChangeQueryConfig, query);
    const records = await query;
    const dtos = this.recordMapper.fromRecordsToDtos(records);
    return dtos;
  }

  private applySearch(query: Knex.QueryBuilder, search: string) {
    return query.where((builder) => {
      builder.where(
        MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CHANGE.ID,
        "like",
        `%${search}%`
      );
    });
  }

  async update(
    dto: Partial<MembershipChangeDto>,
    options: RepositoryOptions
  ): Promise<Partial<MembershipChangeDto>> {
    const client = this.db.getClient(options?.tenantId);
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client
      .table(this.tableName)
      .where(MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CHANGE.ID, dto.id!)
      .update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  async upsertList(
    dtos: Partial<MembershipChangeDto>[],
    options?: RepositoryOptions
  ) {
    const client = this.db.getClient();
    const conflicColumns = ["id"];
    const records = this.recordMapper.fromDtosToRecords(dtos);
    const query = client
      .table(this.tableName)
      .insert(records)
      .onConflict(conflicColumns)
      .merge(this.mergedColumns);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dtos;
  }
}
