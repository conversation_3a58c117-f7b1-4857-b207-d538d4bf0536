import { <PERSON><PERSON> } from "knex";
import { CustomerExternalMappingDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { CustomerExternalMappingRecord } from "@features/infra/databases/records";
import { CustomerExternalMappingRecordMapper } from "@features/infra/databases/record-mappers";
import {
  BaseDao,
  IBaseDao,
  IDatabase,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_TABLE_NAMES,
} from "@constants";

export interface ICustomerExternalMappingDao
  extends IBaseDao<
    CustomerExternalMappingDto,
    CustomerExternalMappingRecord
  > {}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.CUSTOMER_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class CustomerExternalMappingDao
  extends BaseDao<
    CustomerExternalMappingDto,
    CustomerExternalMappingRecord
  >
  implements ICustomerExternalMappingDao
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.CUSTOMER_EXTERNAL_MAPPING,
      recordMapper: new CustomerExternalMappingRecordMapper(),
    });
  }
}
