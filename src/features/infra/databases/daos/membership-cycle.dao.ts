import { <PERSON><PERSON> } from "knex";
import { MembershipCycleDto } from "@features/domain";
import { MembershipCycleRecord } from "@features/infra/databases/records";
import { Dao, DataSource, Lifecycle, Logger, Optional } from "@heronjs/common";
import { MembershipCycleQueryConfig } from "@features/infra/databases/query-configs";
import { MembershipCycleRecordMapper } from "@features/infra/databases/record-mappers";
import {
  BaseDao,
  DaoUtils,
  IBaseDao,
  IDatabase,
  QueryInput,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
  MEMBERSHIP_MODULE_TABLE_NAMES,
} from "@constants";

export interface IMembershipCycleDao
  extends IBaseDao<MembershipCycleDto, MembershipCycleRecord> {}
@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CYCLE,
  scope: Lifecycle.Singleton,
})
export class <PERSON><PERSON>ycle<PERSON>ao
  extends BaseDao<MembershipCycleDto, MembershipCycleRecord>
  implements IMembershipCycleDao
{
  private readonly logger = new Logger(this.constructor.name);
  private readonly mergedColumns = [
    "membership_id",
    "billing_cycle",
    "amount",
    "created_at",
    "created_by",
    "updated_at",
    "updated_by",
  ];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE,
      recordMapper: new MembershipCycleRecordMapper(),
    });
  }
  private buildSelectClause(builder: Knex.QueryBuilder) {
    const query = builder.select(
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.*`,
      builder.client.raw(
        `json_agg(${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING}.*) as external_mappings`
      )
    );
    return query;
  }

  private buildJoinClause(builder: Knex.QueryBuilder) {
    builder.leftJoin(
      MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING.MEMBERSHIP_CYCLE_ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CYCLE.ID}`
    );
  }

  private buildGroupByClause(builder: Knex.QueryBuilder) {
    builder.groupBy(
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CYCLE.ID}`
    );
  }

  private applySearch(builder: Knex.QueryBuilder, search: string) {
    const query = builder.where((builder) => {
      builder.whereILike(
        `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.name`,
        `%${search}%`
      );
    });
    return query;
  }

  async find(
    payload: QueryInput & { search?: string },
    options?: RepositoryOptions
  ): Promise<Partial<MembershipCycleDto>[]> {
    const { offset, limit, sort, filter, search } = payload;
    const client = this.db.getClient(options?.tenantId);
    const query = client.from(this.tableName).modify(this.buildSelectClause);
    if (search) this.applySearch(query, search);
    if (filter) DaoUtils.applyFilter(filter, MembershipCycleQueryConfig, query);
    if (offset !== undefined) query.offset(offset);
    if (limit !== undefined) query.limit(limit);
    if (sort) DaoUtils.applySort(sort, MembershipCycleQueryConfig, query);
    const records = await query;
    const dtos = this.recordMapper.fromRecordsToDtos(records);
    return dtos;
  }

  async findOne(
    payload?: QueryInputFindOne<MembershipCycleDto>,
    options?: RepositoryOptions
  ): Promise<Optional<Partial<MembershipCycleDto>>> {
    const client = this.db.getClient(options?.tenantId);
    const query = client
      .from(this.tableName)
      .modify(this.buildSelectClause)
      .modify(this.buildJoinClause)
      .modify(this.buildGroupByClause);
    if (payload?.filter)
      DaoUtils.applyFilter(payload.filter, MembershipCycleQueryConfig, query);
    if (options?.trx) query.transacting(options.trx);
    const record = await query.first();
    return record ? this.recordMapper.fromRecordToDto(record) : undefined;
  }

  async count(
    payload: QueryInput & { search?: string },
    options?: RepositoryOptions
  ): Promise<number> {
    const { filter, search } = payload;
    const client = this.db.getClient(options?.tenantId);
    const query = client.count("*").from(this.tableName);
    if (search) this.applySearch(query, search);
    if (filter) DaoUtils.applyFilter(filter, MembershipCycleQueryConfig, query);
    const { count } = await query.first();
    return +count;
  }

  async upsertList(
    dtos: Partial<MembershipCycleDto>[],
    options?: RepositoryOptions
  ) {
    const client = this.db.getClient();
    const conflicColumns = ["id"];
    const records = this.recordMapper.fromDtosToRecords(dtos);
    const query = client
      .table(this.tableName)
      .insert(records)
      .onConflict(conflicColumns)
      .merge(this.mergedColumns);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dtos;
  }
}
