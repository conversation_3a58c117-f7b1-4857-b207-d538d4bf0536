import { Nullable } from "@heronjs/common";
import { BillingCycleEnum } from "@features/domain";
import { MembershipCycleExternalMappingRecord } from "@features/infra/databases/records/membership-cycle-external-mapping.record";

export type MembershipCycleRecord = {
  id: string;
  membership_id: string;
  billing_cycle: BillingCycleEnum;
  amount: number;
  created_at: Date;
  created_by: string;
  updated_at: Nullable<Date>;
  updated_by: Nullable<string>;
  external_mappings?: MembershipCycleExternalMappingRecord[];
};
