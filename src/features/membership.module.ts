import { Module } from "@heronjs/common";
import {
  WebhookHandlerFactory,
  PaymentSucceededHandler,
  CreateMembershipUseCase,
  UpdateMembershipUseCase,
  CreateCapabilityUseCase,
  GetMembershipByIdUseCase,
  GetTenantMembershipUseCase,
  GetListOfMembershipsUseCase,
  GetListOfCapabilitiesUseCase,
  AssignTenantMembershipUseCase,
  ExtendTenantTrialPeriodUseCase,
  GetMembershipCapabilitiesUseCase,
  ActivateExternalSubscriptionUseCase,
} from "@features/app";
import {
  PaymentWebhook,
  AdminCapabilityRest,
  AdminMembershipRest,
  TenantMembershipRest,
  AdminTenantMembershipRest,
  InternalTenantMembershipRest,
} from "@features/presentation";
import {
  CapabilityMapper,
  MembershipMapper,
  CapabilityBuilder,
  MembershipBuilder,
  MembershipCycleMapper,
  TenantMembershipMapper,
  MembershipChangeMapper,
  TenantMembershipBuilder,
  MembershipCapabilityMapper,
  CustomerExternalMappingMapper,
  MembershipExternalMappingMapper,
  MembershipCycleExternalMappingMapper,
  TenantMembershipExternalMappingMapper,
} from "@features/domain";
import {
  DatabaseUtil,
  CapabilityDao,
  MembershipDao,
  PaymentService,
  BasicRetryUtil,
  UserProfileService,
  MembershipCycleDao,
  TenantMembershipDao,
  MembershipChangeDao,
  MembershipRepository,
  PaypalPaymentGateway,
  StripePaymentGateway,
  CapabilityRepository,
  PaymentGatewayFactory,
  MembershipCapabilityDao,
  TenantMembershipRepository,
  CustomerExternalMappingDao,
  MembershipExternalMappingDao,
  CustomerExternalMappingRepository,
  MembershipCycleExternalMappingDao,
  TenantMembershipExternalMappingDao,
} from "@features/infra";

@Module({
  controllers: [
    AdminMembershipRest,
    AdminTenantMembershipRest,
    AdminCapabilityRest,
    InternalTenantMembershipRest,
    TenantMembershipRest,
    PaymentWebhook,
  ],
  providers: [
    // Mappers
    CapabilityMapper,
    MembershipMapper,
    MembershipCycleMapper,
    TenantMembershipMapper,
    MembershipChangeMapper,
    MembershipCapabilityMapper,
    CustomerExternalMappingMapper,
    MembershipExternalMappingMapper,
    MembershipCycleExternalMappingMapper,
    TenantMembershipExternalMappingMapper,

    // Builders
    CapabilityBuilder,
    MembershipBuilder,
    TenantMembershipBuilder,

    // DAOs
    CapabilityDao,
    MembershipDao,
    MembershipCycleDao,
    TenantMembershipDao,
    MembershipChangeDao,
    MembershipCapabilityDao,
    CustomerExternalMappingDao,
    MembershipExternalMappingDao,
    MembershipCycleExternalMappingDao,
    TenantMembershipExternalMappingDao,

    // Repositories
    CapabilityRepository,
    MembershipRepository,
    TenantMembershipRepository,
    CustomerExternalMappingRepository,

    // Membership Use Cases
    CreateMembershipUseCase,
    UpdateMembershipUseCase,
    GetMembershipByIdUseCase,
    GetListOfMembershipsUseCase,

    // Tenant Membership Use Cases
    GetTenantMembershipUseCase,
    AssignTenantMembershipUseCase,
    ExtendTenantTrialPeriodUseCase,
    ActivateExternalSubscriptionUseCase,

    // Capability Use Cases
    CreateCapabilityUseCase,
    GetListOfCapabilitiesUseCase,

    // Membership Capability Use Cases
    GetMembershipCapabilitiesUseCase,

    // Utils
    BasicRetryUtil,
    DatabaseUtil,

    // Factories
    PaymentGatewayFactory,
    WebhookHandlerFactory,

    // External Services
    PaymentService,
    UserProfileService,

    // Payment Gateways
    StripePaymentGateway,
    PaypalPaymentGateway,

    // Handlers
    PaymentSucceededHandler,
  ],
})
// @Transporters([KafkaAdapter])
export class MembershipModule {}
