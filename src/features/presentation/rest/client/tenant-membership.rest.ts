import {
  <PERSON>,
  Body,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>m,
  Principal,
} from "@heronjs/common";
import {
  IActivateExternalSubscriptionUseCase,
  ActivateExternalSubscriptionUseCaseInput,
} from "@features/app";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";

@Rest("/tenant-memberships")
export class TenantMembershipRest {
  @Post({ uri: "/:id/activate", code: 201 })
  @Guard({ private: true })
  async assign(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.ACTIVATE_EXTERNAL_SUBSCRIPTION)
    useCase: IActivateExternalSubscriptionUseCase,
    @Principal("sub") authId: string,
    @Param("id") tenantMembershipId: string,
    @Body() body: ActivateExternalSubscriptionUseCaseInput
  ) {
    const result = await useCase.exec(
      { tenantMembershipId },
      { auth: { authId } }
    );
    return result;
  }
}
