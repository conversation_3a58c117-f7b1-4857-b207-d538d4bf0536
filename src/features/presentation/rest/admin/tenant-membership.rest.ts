import {
  Rest,
  Body,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>m,
  Principal,
} from "@heronjs/common";
import {
  IAssignTenantMembershipUseCase,
  AssignTenantMembershipUseCaseInput,
  IExtendTenantTrialPeriodUseCase,
  ExtendTenantTrialPeriodUseCaseInput,
} from "@features/app";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";

@Rest("/admin/tenant-memberships")
export class AdminTenantMembershipRest {
  @Post({ uri: "/", code: 201 })
  @Guard({ private: true })
  async assign(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.ASSIGN_TENANT_MEMBERSHIP)
    useCase: IAssignTenantMembershipUseCase,
    @Principal("sub") authId: string,
    @Body() body: AssignTenantMembershipUseCaseInput
  ) {
    const result = await useCase.exec(
      { ...body, createdBy: authId },
      { auth: { authId } }
    );
    return result;
  }

  @Post({ uri: "/:id/extend-trial", code: 204 })
  @Guard({ private: true })
  async extendTrial(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.EXTEND_TENANT_TRIAL_PERIOD)
    useCase: IExtendTenantTrialPeriodUseCase,
    @Param("id") tenantMembershipId: string,
    @Principal("sub") authId: string,
    @Body() body: ExtendTenantTrialPeriodUseCaseInput
  ) {
    const result = await useCase.exec(
      { ...body, tenantMembershipId },
      { auth: { authId } }
    );
    return result;
  }
}
