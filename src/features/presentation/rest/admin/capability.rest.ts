import {
  Get,
  Rest,
  Body,
  <PERSON><PERSON>,
  Post,
  Guard,
  Queries,
  Principal,
} from "@heronjs/common";
import {
  ICreateCapabilityUseCase,
  CreateCapabilityUseCaseInput,
  IGetListOfCapabilitiesUseCase,
} from "@features/app";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";

@Rest("/admin/capabilities")
export class AdminCapabilityRest {
  @Post({ uri: "/", code: 201 })
  @Guard({ private: true })
  async create(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.CREATE_CAPABILITY)
    useCase: ICreateCapabilityUseCase,
    @Principal("sub") authId: string,
    @Body() body: CreateCapabilityUseCaseInput
  ) {
    const result = await useCase.exec(body, { auth: { authId } });
    return result;
  }

  @Get({ uri: "/" })
  @Guard({ private: true })
  async getList(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.GET_LIST_OF_CAPABILITIES)
    useCase: IGetListOfCapabilitiesUseCase,
    @Principal("sub") authId: string,
    @Queries() queries: any
  ) {
    const result = await useCase.exec({}, { auth: { authId } });
    return result;
  }
}
