import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { InternalApiKeyInterceptor } from "@interceptors";
import { IGetTenantMembershipUseCase } from "@features/app";
import { Get, Rest, Fuse, Param, UseInterceptors } from "@heronjs/common";

@Rest("/internal/tenant-memberships")
export class InternalTenantMembershipRest {
  @Get({ uri: "/:id" })
  @UseInterceptors([InternalApiKeyInterceptor])
  async getById(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.GET_TENANT_MEMBERSHIP)
    useCase: IGetTenantMembershipUseCase,
    @Param("id") id: string
  ) {
    return useCase.exec({ id });
  }
}
